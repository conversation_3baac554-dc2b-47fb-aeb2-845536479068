package ai

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai/functions"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	utilErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const (
	runbooksAnnotation = "akuity.io/runbooks"
	apiCallTimeout     = time.Second * 5
)

// equalStringSlices compares two string slices for equality
func equalStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

func (s *Service) AutoCreateIncidents(ctx context.Context, instance *models.ArgoCDInstance, tenantStateClient tenant.StateClient) error {
	instanceConfig, err := s.resourceRepoSet.GetRepoSet().ArgoCDInstanceConfigs().Filter(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(instance.ID)).One(ctx, "spec")
	if err != nil {
		return err
	}
	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return err
	}
	if _, ok := lo.Find(spec.KubeVisionConfig.AIConfig.Incidents.Triggers, func(selector models.TargetSelector) bool {
		return len(selector.ArgoCDApplications) > 0
	}); ok {
		if err := s.createAppsIncidents(ctx, instance, tenantStateClient, spec); err != nil {
			return err
		}
	}
	if _, ok := lo.Find(spec.KubeVisionConfig.AIConfig.Incidents.Triggers, func(selector models.TargetSelector) bool {
		return len(selector.K8SNamespaces) > 0
	}); ok {
		if err := s.createNamespacesIncidents(ctx, instance, spec); err != nil {
			return err
		}
	}
	return nil
}

func getRunbooks(spec models.InstanceConfigSpec, annotations map[string]string, matcher func(selector models.TargetSelector) bool) (bool, []string) {
	allRunbooks := lo.Map(spec.KubeVisionConfig.AIConfig.Runbooks, func(item models.Runbook, index int) string {
		return item.Name
	})
	runbooks := lo.Map(lo.Filter(spec.KubeVisionConfig.AIConfig.Runbooks, func(item models.Runbook, index int) bool {
		return matcher(item.AppliedTo)
	}), func(item models.Runbook, index int) string {
		return item.Name
	})

	if annotations == nil {
		annotations = map[string]string{}
	}
	if val, annotationsOk := annotations[runbooksAnnotation]; annotationsOk {
		runbooks = append(runbooks, strings.Split(val, ",")...)
	}

	_, createIncident := lo.Find(spec.KubeVisionConfig.AIConfig.Incidents.Triggers, matcher)
	return createIncident, lo.Filter(lo.Uniq(runbooks), func(item string, index int) bool {
		return lo.Contains(allRunbooks, item)
	})
}

func matchGlob(pattern, str string) bool {
	ok, err := filepath.Match(pattern, str)
	return ok && err == nil
}

func (s *Service) createNamespacesIncidents(ctx context.Context, instance *models.ArgoCDInstance, spec models.InstanceConfigSpec) error {
	var namespaces []struct {
		ClusterID string `boil:"cluster_id" json:"cluster_id"`
		Namespace string `boil:"namespace" json:"namespace"`
	}
	if err := models.NewQuery(qm.SQL(`
select distinct obj.cluster_id, obj.namespace
from argo_cd_cluster_k8s_object obj
where obj.argocd_application_info ->> 'health_status' = 'Degraded'`),
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instance.ID),
	).Bind(ctx, s.db, &namespaces); err != nil {
		return fmt.Errorf("failed to get degraded namespaces: %w", err)
	}
	nsByCluster := map[string][]string{}
	for _, ns := range namespaces {
		nsByCluster[ns.ClusterID] = append(nsByCluster[ns.ClusterID], ns.Namespace)
	}

	for clusterID, nsNames := range nsByCluster {
		k8sClient, err := s.k8sClientSet.GetK8sClient(ctx, instance.ID, clusterID)
		if err != nil {
			return fmt.Errorf("failed to get k8s client for cluster %s: %w", clusterID, err)
		}
		if k8sClient == nil {
			continue
		}
		for _, nsName := range nsNames {
			if nsName == "" {
				continue
			}

			incident := &models.Incident{
				InstanceID: instance.ID,
				ClusterID:  clusterID,
				Namespace:  nsName,
			}

			if createIncident, _, err := s.InferRunbooks(ctx, instance.ID, spec, incident); err != nil {
				return fmt.Errorf("failed to infer runbooks for namespace %s in cluster %s: %w", nsName, clusterID, err)
			} else if !createIncident {
				continue
			}
			if _, err := s.CreateIncident(ctx, nil, instance.ID, instance.OrganizationOwner, incident); err != nil {
				return fmt.Errorf("failed to create incident for namespace %s in cluster %s: %w", nsName, clusterID, err)
			}
		}
	}

	return nil
}

func nestedString(obj map[string]interface{}, fields ...string) string {
	val, _, _ := unstructured.NestedString(obj, fields...)
	return val
}

// InferRunbooks populates the runbooks for the given incident based on the instance configuration spec
func (s *Service) InferRunbooks(ctx context.Context, instanceID string, spec models.InstanceConfigSpec, incident *models.Incident) (bool, []string, error) {
	createIncident := false
	var runbooks []string
	switch {
	case incident.Application != "":
		tnt, err := agentclient.NewArgoCDTenant(s.hostRestConfig, s.log, instanceID)
		if err != nil {
			return false, nil, fmt.Errorf("failed to create tenant client: %w", err)
		}
		k3sDynamicClient, err := tnt.ControlPlaneDynamicClientset(ctx)
		if err != nil {
			return false, nil, fmt.Errorf("failed to create dynamic client: %w", err)
		}
		getCtx, cancel := context.WithTimeout(ctx, apiCallTimeout)
		app, err := k3sDynamicClient.Resource(misc.ApplicationGVR).Namespace(argoproj.K3sArgoCDNamespace).Get(getCtx, incident.Application, metav1.GetOptions{})
		cancel()
		if err != nil {
			if utilErrors.IsInstanceReadinessError(err) {
				return false, nil, utilErrors.NewRetryableError(err, "failed to get Argo CD application, will retry")
			}
			if k8serrors.IsNotFound(err) {
				return false, nil, nil
			}
			return false, nil, fmt.Errorf("failed to get Argo CD application %s: %w", incident.Application, err)
		}
		clusterName := argocd.GetNameFromDestination(argocd.ApplicationDestination{
			Server:    nestedString(app.Object, "spec", "destination", "server"),
			Name:      nestedString(app.Object, "spec", "destination", "name"),
			Namespace: nestedString(app.Object, "spec", "destination", "namespace"),
		})
		createIncident, runbooks = getRunbooks(spec, app.GetAnnotations(), func(trigger models.TargetSelector) bool {
			if _, ok := lo.Find(trigger.ArgoCDApplications, func(item string) bool {
				return matchGlob(item, app.GetName())
			}); !ok {
				return false
			}
			if _, ok := lo.Find(trigger.Clusters, func(item string) bool {
				return matchGlob(item, clusterName)
			}); !ok && len(trigger.Clusters) > 0 {
				return false
			}
			return true
		})
		return createIncident, runbooks, nil
	case incident.ClusterID != "" && incident.Namespace != "":
		k8sClient, err := s.k8sClientSet.GetK8sClient(ctx, instanceID, incident.ClusterID)
		if err != nil {
			return false, nil, fmt.Errorf("failed to get k8s client for cluster %s: %w", incident.ClusterID, err)
		}
		if k8sClient == nil {
			return false, runbooks, nil
		}
		getCtx, cancel := context.WithTimeout(ctx, apiCallTimeout)
		ns, err := k8sClient.KubernetesClientset.CoreV1().Namespaces().Get(getCtx, incident.Namespace, metav1.GetOptions{})
		cancel()
		if err != nil {
			if utilErrors.IsInstanceReadinessError(err) {
				return false, nil, utilErrors.NewRetryableError(err, "failed to get namespace, will retry")
			}
			if k8serrors.IsNotFound(err) {
				return false, nil, nil
			}
			return false, nil, err
		}
		cluster, err := s.resourceRepoSet.ArgoCDClusters().GetByID(ctx, incident.ClusterID)
		if err != nil {
			return false, nil, fmt.Errorf("failed to get cluster %s: %w", incident.ClusterID, err)
		}
		createIncident, runbooks = getRunbooks(spec, ns.GetAnnotations(), func(selector models.TargetSelector) bool {
			if _, ok := lo.Find(selector.K8SNamespaces, func(item string) bool {
				return matchGlob(item, incident.Namespace)
			}); !ok {
				return false
			}
			if _, ok := lo.Find(selector.Clusters, func(item string) bool {
				return matchGlob(item, cluster.Name)
			}); !ok && len(selector.Clusters) > 0 {
				return false
			}
			return true
		})
		return createIncident, runbooks, nil
	}
	return false, runbooks, errors.New("incident must have either application or cluster and namespace defined")
}

func (s *Service) createAppsIncidents(ctx context.Context, instance *models.ArgoCDInstance, tenantStateClient tenant.StateClient, spec models.InstanceConfigSpec) error {
	apps, err := tenantStateClient.GetApplications(ctx, instance.ID)
	if err != nil {
		return fmt.Errorf("failed to get applications for instance %s: %w", instance.ID, err)
	}
	for app, err := range apps {
		if err != nil {
			s.log.Error(err, fmt.Sprintf("failed to get application %s", app.Name))
			continue
		}
		if app.Status.Health.Status != argocd.HealthStatusDegraded {
			continue
		}
		incident := &models.Incident{
			InstanceID:  instance.ID,
			Application: app.Name,
		}

		if createIncident, _, err := s.InferRunbooks(ctx, instance.ID, spec, incident); err != nil {
			if utilErrors.IsInstanceReadinessError(err) {
				continue
			}
			s.log.Error(err, fmt.Sprintf("failed to infer runbooks for application %s", app.Name))
			continue
		} else if !createIncident {
			continue
		}
		if _, err := s.CreateIncident(ctx, nil, instance.ID, instance.OrganizationOwner, incident); err != nil {
			return fmt.Errorf("failed to create incident for application %s: %w", app.Name, err)
		}
	}
	return nil
}

// IncidentFromContexts creates an incident from the provided AI message contexts.
func (s *Service) IncidentFromContexts(ctx context.Context, instanceID string, contexts []*organizationv1.AIMessageContext) (*models.Incident, error) {
	if len(contexts) != 1 {
		return nil, status.Error(codes.InvalidArgument, "exactly one context is required for incident conversations")
	}
	convCtx := contexts[0]
	incident := models.Incident{InstanceID: instanceID}
	switch {
	case convCtx.K8SNamespace != nil:
		incident.Namespace = convCtx.K8SNamespace.Name
		incident.ClusterID = convCtx.K8SNamespace.ClusterId
	case convCtx.ArgoCdApp != nil:
		incident.Application = convCtx.ArgoCdApp.Name
	default:
		return nil, status.Error(codes.InvalidArgument, "selected context is not supported for incident conversations")
	}

	cfg, err := s.resourceRepoSet.GetRepoSet().ArgoCDInstanceConfigs().Filter(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(instanceID)).One(ctx)
	if err != nil {
		return nil, err
	}
	spec, err := cfg.GetSpec()
	if err != nil {
		return nil, err
	}
	if _, _, err := s.InferRunbooks(ctx, instanceID, spec, &incident); err != nil {
		return nil, err
	}

	return &incident, nil
}

func (s *Service) CreateIncident(ctx context.Context, actor *accesscontrol.Actor, instanceID, orgID string, incident *models.Incident) (*organizationv1.AIConversation, error) {
	var title string
	var convContext models.ConversationContext
	var filterMod qm.QueryMod
	if incident.Application != "" {
		filterMod = qm.And("(metadata->'incident'->>'application')::text = ?", incident.Application)
		title = fmt.Sprintf("Resolving incident for Argo CD application '%s'", incident.Application)
		convContext = models.ConversationContext{
			ArgoCDApp: &models.ArgoCDApplicationConversationContext{InstanceID: instanceID, Name: incident.Application},
		}
	} else if incident.ClusterID != "" && incident.Namespace != "" {
		filterMod = qm.Expr(
			qm.And("(metadata->'incident'->>'clusterId')::text = ?", incident.ClusterID),
			qm.And("(metadata->'incident'->>'namespace')::text = ?", incident.Namespace),
		)
		title = fmt.Sprintf("Resolving incident for namespace '%s'", incident.Namespace)
		convContext = models.ConversationContext{
			K8SNamespace: &models.K8SNamespaceConversationContext{InstanceID: instanceID, ClusterID: incident.ClusterID, Name: incident.Namespace},
		}
	} else {
		return nil, status.Error(codes.InvalidArgument, "incident must have either application or cluster and namespace defined")
	}
	// TODO(hanxiaop): add kargo project incident support

	instance, err := s.resourceRepoSet.ArgoCDInstances(
		models.ArgoCDInstanceWhere.ID.EQ(instanceID),
	).One(ctx)
	if err != nil {
		return nil, err
	}
	conversation, err := s.resourceRepoSet.AIConversations().Filter(
		models.AiConversationWhere.InstanceID.EQ(null.StringFrom(instanceID)),
		qm.And("metadata->'incident' IS NOT NULL"),
		qm.And("(metadata->'incident'->>'resolvedAt') is null"),
		filterMod,
	).One(ctx)

	if err == nil {
		// Even if we found an existing conversation, we need to ensure the runbooks are correct for this incident
		// Get instance configuration to infer runbooks for the current incident
		cfg, err := s.resourceRepoSet.GetRepoSet().ArgoCDInstanceConfigs().Filter(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(instanceID)).One(ctx)
		if err != nil {
			return nil, err
		}
		spec, err := cfg.GetSpec()
		if err != nil {
			return nil, err
		}

		// Infer runbooks for this specific incident
		_, runbooks, err := s.InferRunbooks(ctx, instanceID, spec, incident)
		if err != nil {
			return nil, err
		}

		// Update the conversation's runbooks if they differ from the current incident's runbooks
		metadata, err := conversation.GetMetadata()
		if err != nil {
			return nil, err
		}
		if metadata != nil {
			currentRunbooks := metadata.GetRunbooks()
			// Only update if runbooks are different to avoid unnecessary database writes
			if !equalStringSlices(currentRunbooks, runbooks) {
				metadata.SetRunbooks(runbooks)
				if err := conversation.SetMetadata(metadata); err != nil {
					return nil, err
				}
				// Save the updated conversation
				if err := s.resourceRepoSet.AIConversations().Update(ctx, conversation); err != nil {
					return nil, err
				}
			}
		}

		return s.mapConversationToAPI(ctx, conversation, nil, instance, true)
	}
	if !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if incident.Summary != "" {
		title = incident.Summary
	}
	conversation = &models.AiConversation{
		InstanceID:     null.StringFrom(instanceID),
		OrganizationID: orgID,
		Title:          null.StringFrom(title),
		Public:         null.BoolFrom(true),
	}

	if actor == nil || actor.Type != accesscontrol.ActorTypeUser {
		conversation.UserID = "assistant"
	} else {
		conversation.UserID = s.generateUserID(actor)
	}

	// Get instance configuration to infer runbooks
	cfg, err := s.resourceRepoSet.GetRepoSet().ArgoCDInstanceConfigs().Filter(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(instanceID)).One(ctx)
	if err != nil {
		return nil, err
	}
	spec, err := cfg.GetSpec()
	if err != nil {
		return nil, err
	}

	// Infer runbooks for this incident
	_, runbooks, err := s.InferRunbooks(ctx, instanceID, spec, incident)
	if err != nil {
		return nil, err
	}

	// Create metadata with incident
	metadata := &models.AIConversationMetadata{
		Incident: incident,
	}
	// Use unified runbooks field
	if len(runbooks) > 0 {
		metadata.SetRunbooks(runbooks)
	}
	if err := conversation.SetMetadata(metadata); err != nil {
		return nil, err
	}
	ctxs := []models.ConversationContext{convContext}
	if err := conversation.SetContexts(ctxs); err != nil {
		return nil, err
	}
	id, err := client.NanoID(client.DefaultNanoIDLength)
	if err != nil {
		return nil, err
	}
	contextsData, err := json.Marshal(ctxs)
	if err != nil {
		return nil, err
	}
	messages := []*models.AIConversationMessage{
		{
			ID: id,
			Message: ai.Message{
				Role:    ai.MessageRoleDeveloper,
				Content: fmt.Sprintf("New contexts: %s", string(contextsData)),
			},
		},
	}
	if incident.Summary != "" {
		messages = append(messages, &models.AIConversationMessage{
			ID: id,
			Message: ai.Message{
				Role:    ai.MessageRoleDeveloper,
				Content: fmt.Sprintf("Incident description: %s", incident.Summary),
			},
		})
	}
	if err := conversation.SetMessages(messages); err != nil {
		return nil, err
	}

	if err := s.resourceRepoSet.AIConversations().Create(ctx, conversation); err != nil {
		return nil, err
	}
	return s.mapConversationToAPI(ctx, conversation, nil, instance, true)
}

func (s *Service) getIncidentSystemPrompts(ctx context.Context) ([]ai.Message, error) {
	meta := functions.GetConversationMeta(ctx)
	if meta == nil || meta.Incident == nil {
		return nil, nil
	}
	cfg, err := s.resourceRepoSet.GetRepoSet().ArgoCDInstanceConfigs().GetByID(ctx, meta.Incident.InstanceID)
	if err != nil {
		return nil, err
	}
	spec, err := cfg.GetSpec()
	if err != nil {
		return nil, err
	}

	runbookMsg := `You are working on resolving an incident related to the provided context.
Start from gathering information and understand what is happening. MAKE SURE TO update incident summary and root cause
with high level description of what is happening. Try to keep description up to date and update it as soon
as you get new information.
You will be provided with runbooks. Following the instructions in the runbook to resolve the incident.
While resolving the incident use tools to update incident status:
* "update-incident-summary" - update the incident summary with high level summary of what is happening
* "update-incident-root-cause" - once you determine the root cause of the incident, update the incident root cause
* "resolve-incident" - once the incident is resolved, update the incident resolution. IMPORTANT: resolve incident only if user explicitly asks you to do so or if it is explicitly instructed in runbook.

Restrictions:
* never automatically generate or store runbooks during incident handling
* only use "store-runbook" when user explicitly asks with phrases like "store this runbook", "save the runbook", "create a runbook for this issue"
* only generate runbooks when user explicitly asks with phrases like "generate runbook", "create runbook", "make a runbook"
* the runbook "stored" field should only be set to "true" after the "store-runbook" tool has been successfully executed. In all other cases (including runbook generation, incident handling), it should be "false". The runbook "content" field should only be populated when "stored" is "true", otherwise it should be an empty string.

If there are multiple runbooks, try to execute the runbooks one by one.`
	runbooks := meta.GetRunbooks()
	if len(runbooks) > 0 {
		runbookMsg += `
The following are the runbooks that you can use to resolve the incident:
`
		bookByName := lo.KeyBy(spec.KubeVisionConfig.AIConfig.Runbooks, func(item models.Runbook) string {
			return item.Name
		})
		for _, name := range runbooks {
			runbook, ok := bookByName[name]
			if !ok {
				continue
			}
			runbookMsg += fmt.Sprintf(`Runbook: "%s", Steps: %s
`, runbook.Name, runbook.Content)
		}
	}
	prompts := []ai.Message{
		{
			Role:    ai.MessageRoleSystem,
			Content: runbookMsg,
		},
	}
	return prompts, nil
}
