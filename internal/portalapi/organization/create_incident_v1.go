package organization

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"k8s.io/client-go/util/jsonpath"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	metadatautil "github.com/akuityio/akuity-platform/pkg/utils/grpc/metadata"
)

func (s *OrganizationV1Server) CreateIncident(
	ctx context.Context,
	req *organizationv1.CreateIncidentRequest,
) (*organizationv1.CreateIncidentResponse, error) {
	actor, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionCreateAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}

	cfg, err := s.RepoSet.ArgoCDInstanceConfigs().Filter(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(req.GetInstanceId())).One(ctx)
	if err != nil {
		return nil, err
	}

	spec, err := cfg.GetSpec()
	if err != nil {
		return nil, err
	}
	webhook, ok := lo.Find(spec.KubeVisionConfig.AIConfig.Incidents.Webhooks, func(item models.IncidentWebhookConfig) bool {
		return item.Name == req.GetWebhookName()
	})
	if !ok {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("webhook %q is not configured", req.GetWebhookName()))
	}

	q, err := queryFromContext(ctx)
	if err != nil {
		return nil, err
	}

	incident := models.Incident{}
	clusterName := ""
	for _, item := range []struct {
		name string
		path string
		val  *string
	}{{
		name: "Namespace",
		path: webhook.K8SNamespacePath,
		val:  &incident.Namespace,
	}, {
		name: "Cluster",
		path: webhook.ClusterPath,
		val:  &clusterName,
	}, {
		name: "Argo CD Application",
		path: webhook.ArgoCDApplicationNamePath,
		val:  &incident.Application,
	}, {
		name: "Description",
		path: webhook.DescriptionPath,
		val:  &incident.Summary,
	}} {
		if item.path != "" {
			val, err := evalJsonPath(item.name, item.path, map[string]interface{}{"body": req.Body.AsMap(), "query": q})
			if err != nil {
				return nil, err
			}
			*item.val = val
		}
	}
	incident.InstanceID = req.GetInstanceId()
	if clusterName != "" {
		cluster, err := s.RepoSet.ArgoCDClusters().Filter(
			models.ArgoCDClusterWhere.InstanceID.EQ(req.GetInstanceId()),
			models.ArgoCDClusterWhere.Name.EQ(clusterName),
		).One(ctx)
		if err != nil {
			return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("failed to get cluster %q: %v", clusterName, err))
		}
		incident.ClusterID = cluster.ID
	}
	if _, _, err := aiSvc.InferRunbooks(ctx, req.GetInstanceId(), spec, &incident); err != nil {
		return nil, err
	}

	if _, err := aiSvc.CreateIncident(ctx, actor, req.GetInstanceId(), req.OrganizationId, &incident); err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("failed to create incident: %v", err))
	}
	return &organizationv1.CreateIncidentResponse{}, nil
}

func evalJsonPath(
	name string,
	path string,
	in map[string]interface{},
) (string, error) {
	jp := jsonpath.New(name).AllowMissingKeys(true)
	if err := jp.Parse(path); err != nil {
		return "", status.Error(codes.InvalidArgument, fmt.Sprintf("failed to parse %s JSONPath: %v", name, err))
	}
	res, err := jp.FindResults(in)
	if err != nil {
		return "", status.Error(codes.InvalidArgument, fmt.Sprintf("failed to evaluate %s JSONPath: %v", path, err))
	}
	valueParts := make([]string, 0)
	for _, result := range res {
		for _, rv := range result {
			valueStr := fmt.Sprintf("%v", rv.Interface())
			valueParts = append(valueParts, valueStr)
		}
	}
	return strings.Join(valueParts, ","), nil
}

func queryFromContext(ctx context.Context) (map[string]string, error) {
	q := map[string]string{}
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return q, nil
	}
	urlStr, ok := metadatautil.GetRequestURL(md)
	if !ok {
		return q, nil
	}
	parsed, err := url.Parse(urlStr)
	if err != nil {
		return nil, err
	}
	for key, vals := range parsed.Query() {
		if len(vals) > 0 {
			q[key] = vals[0]
		}
	}

	return q, nil
}
